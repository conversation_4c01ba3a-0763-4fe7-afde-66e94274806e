<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内网地址测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .example-urls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .example-urls h4 {
            margin-top: 0;
            color: #495057;
        }
        .example-urls ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .example-urls li {
            margin: 5px 0;
            font-family: monospace;
            background: white;
            padding: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 内网地址智能识别测试</h1>
        <p>此页面用于测试网站信息抓取功能对内网地址的支持情况。</p>

        <div class="example-urls">
            <h4>📋 测试用例示例：</h4>
            <ul>
                <li>http://*************:8080</li>
                <li>http://localhost:3000</li>
                <li>http://jenkins.local</li>
                <li>http://*********:9000</li>
                <li>http://admin.internal</li>
                <li>http://***********:8848</li>
                <li>http://grafana.corp</li>
                <li>http://127.0.0.1:5601</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔍 内网地址检测测试</h3>
            <input type="url" id="testUrl" class="test-input" placeholder="输入要测试的URL（如：http://*************:8080）">
            <button onclick="testInternalNetwork()" class="test-button" id="testBtn">开始测试</button>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🌐 网站信息抓取测试</h3>
            <input type="url" id="fetchUrl" class="test-input" placeholder="输入要抓取信息的URL">
            <button onclick="testFetchMetadata()" class="test-button" id="fetchBtn">抓取网站信息</button>
            <div id="fetchResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- 引入工具类 -->
    <script src="assets/js/utils/helpers.js"></script>

    <script>
        // 测试内网地址检测
        function testInternalNetwork() {
            const url = document.getElementById('testUrl').value.trim();
            const resultDiv = document.getElementById('testResult');
            const btn = document.getElementById('testBtn');

            if (!url) {
                showResult(resultDiv, '请输入要测试的URL', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = '检测中...';

            try {
                // 检查工具类是否加载
                if (!window.Utils || !window.Utils.WebsiteMeta) {
                    throw new Error('工具类未正确加载');
                }

                const isInternal = window.Utils.WebsiteMeta.isInternalNetwork(url);
                
                let result = `🔍 检测结果：\n`;
                result += `URL: ${url}\n`;
                result += `是否为内网地址: ${isInternal ? '✅ 是' : '❌ 否'}\n\n`;
                
                if (isInternal) {
                    result += `🏠 内网地址类型分析：\n`;
                    
                    const urlObj = new URL(url);
                    const hostname = urlObj.hostname;
                    const port = urlObj.port;
                    
                    // 分析IP类型
                    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
                    if (ipRegex.test(hostname)) {
                        const parts = hostname.split('.').map(Number);
                        if (parts[0] === 10) {
                            result += `- IP类型: 10.x.x.x (Class A 私有网络)\n`;
                        } else if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) {
                            result += `- IP类型: 172.16-31.x.x (Class B 私有网络)\n`;
                        } else if (parts[0] === 192 && parts[1] === 168) {
                            result += `- IP类型: 192.168.x.x (Class C 私有网络)\n`;
                        } else if (parts[0] === 127) {
                            result += `- IP类型: 127.x.x.x (本地回环地址)\n`;
                        }
                    } else {
                        result += `- 域名类型: ${hostname} (内网域名)\n`;
                    }
                    
                    if (port) {
                        result += `- 端口: ${port}\n`;
                    }
                    
                    result += `\n✨ 将使用内网地址专用处理策略`;
                } else {
                    result += `🌐 外网地址，将使用代理服务获取信息`;
                }

                showResult(resultDiv, result, 'success');
            } catch (error) {
                showResult(resultDiv, `❌ 检测失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '开始测试';
            }
        }

        // 测试网站信息抓取
        async function testFetchMetadata() {
            const url = document.getElementById('fetchUrl').value.trim();
            const resultDiv = document.getElementById('fetchResult');
            const btn = document.getElementById('fetchBtn');

            if (!url) {
                showResult(resultDiv, '请输入要抓取信息的URL', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = '抓取中...';

            try {
                // 检查工具类是否加载
                if (!window.Utils || !window.Utils.WebsiteMeta) {
                    throw new Error('工具类未正确加载');
                }

                showResult(resultDiv, '🔍 开始抓取网站信息...', 'info');

                const metadata = await window.Utils.WebsiteMeta.fetchWebsiteMetadata(url);
                
                let result = `🎉 抓取完成！\n\n`;
                result += `📊 抓取结果：\n`;
                result += `- 标题: ${metadata.title || '未获取到'}\n`;
                result += `- 描述: ${metadata.description || '未获取到'}\n`;
                result += `- 分类: ${metadata.category || '未分类'}\n`;
                result += `- 关键词: ${metadata.keywords?.join(', ') || '无'}\n`;
                result += `- 图标: ${metadata.favicon ? '已获取' : '未获取'}\n`;
                result += `- 成功状态: ${metadata.success ? '✅ 成功' : '❌ 失败'}\n`;
                result += `- 数据源: ${metadata.source || '未知'}\n`;
                
                if (metadata.error) {
                    result += `- 错误信息: ${metadata.error}\n`;
                }

                result += `\n📋 完整数据：\n${JSON.stringify(metadata, null, 2)}`;

                showResult(resultDiv, result, metadata.success ? 'success' : 'error');
            } catch (error) {
                showResult(resultDiv, `❌ 抓取失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '抓取网站信息';
            }
        }

        // 显示结果
        function showResult(element, message, type) {
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 内网地址测试页面已加载');
            
            // 检查工具类是否正确加载
            if (window.Utils && window.Utils.WebsiteMeta) {
                console.log('✅ 工具类加载成功');
            } else {
                console.error('❌ 工具类加载失败');
                alert('工具类加载失败，请检查 assets/js/utils/helpers.js 文件是否存在');
            }
        });
    </script>
</body>
</html>
