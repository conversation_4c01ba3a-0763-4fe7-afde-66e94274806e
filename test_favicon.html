<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon获取测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 16px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .favicon-preview {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .favicon-preview img {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-urls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-urls h4 {
            margin-top: 0;
            color: #495057;
        }
        .test-urls button {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin: 2px;
            font-size: 12px;
        }
        .test-urls button:hover {
            background: #218838;
        }
        .batch-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .batch-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .batch-item.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .batch-item.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .batch-item h5 {
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        .batch-item .favicon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Favicon获取优化测试</h1>
        <p>此页面用于测试优化后的favicon获取功能，包括HTML解析、多路径测试和内网支持。</p>

        <div class="test-urls">
            <h4>🔗 快速测试地址：</h4>
            <button onclick="fillUrl('https://www.google.com')">Google</button>
            <button onclick="fillUrl('https://github.com')">GitHub</button>
            <button onclick="fillUrl('https://www.baidu.com')">百度</button>
            <button onclick="fillUrl('https://stackoverflow.com')">Stack Overflow</button>
            <button onclick="fillUrl('https://www.bilibili.com')">哔哩哔哩</button>
            <button onclick="fillUrl('http://*************:8080')">内网IP</button>
            <button onclick="fillUrl('http://localhost:3000')">Localhost</button>
            <button onclick="fillUrl('http://jenkins.local')">内网域名</button>
        </div>

        <div class="test-section">
            <h3>🔍 单个URL测试</h3>
            <input type="url" id="testUrl" class="test-input" placeholder="输入要测试的URL">
            <button onclick="testSingleUrl()" class="test-button" id="singleTestBtn">测试Favicon获取</button>
            <button onclick="testFaviconUrls()" class="test-button">查看候选URLs</button>
            <div id="singleResult" class="result" style="display: none;"></div>
            <div id="faviconPreview" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🚀 批量测试</h3>
            <button onclick="runBatchTest()" class="test-button" id="batchTestBtn">运行批量测试</button>
            <button onclick="clearBatchResults()" class="test-button" style="background: #6c757d;">清空结果</button>
            <div id="batchProgress" style="display: none; margin: 15px 0;">
                <div style="background: #e9ecef; border-radius: 10px; overflow: hidden;">
                    <div id="progressBar" style="height: 20px; background: #007bff; width: 0%; transition: width 0.3s;"></div>
                </div>
                <p id="progressText" style="margin: 5px 0; font-size: 14px;">准备中...</p>
            </div>
            <div id="batchResults" class="batch-test"></div>
        </div>
    </div>

    <!-- 引入工具类 -->
    <script src="assets/js/utils/helpers.js"></script>

    <script>
        let testing = false;

        // 快速填充URL
        function fillUrl(url) {
            document.getElementById('testUrl').value = url;
            showResult('singleResult', `已填充URL: ${url}`, 'info');
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 隐藏结果
        function hideResult(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        // 测试单个URL的favicon获取
        async function testSingleUrl() {
            if (testing) return;

            const url = document.getElementById('testUrl').value.trim();
            if (!url) {
                showResult('singleResult', '请输入要测试的URL', 'error');
                return;
            }

            testing = true;
            const btn = document.getElementById('singleTestBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';

            try {
                // 检查工具类是否加载
                if (!window.Utils || !window.Utils.Url) {
                    throw new Error('工具类未正确加载');
                }

                showResult('singleResult', '🔍 开始获取favicon...', 'info');

                const startTime = Date.now();
                const faviconUrl = await window.Utils.Url.getBestFavicon(url);
                const endTime = Date.now();

                if (faviconUrl) {
                    const duration = endTime - startTime;
                    showResult('singleResult', `✅ Favicon获取成功！\n耗时: ${duration}ms\nURL: ${faviconUrl}`, 'success');
                    
                    // 显示favicon预览
                    const previewDiv = document.getElementById('faviconPreview');
                    previewDiv.innerHTML = `
                        <div class="favicon-preview">
                            <img src="${faviconUrl}" alt="Favicon" onerror="this.style.display='none'">
                            <span>预览: ${faviconUrl}</span>
                        </div>
                    `;
                    previewDiv.style.display = 'block';
                } else {
                    showResult('singleResult', '❌ 未能获取到有效的favicon', 'error');
                    hideResult('faviconPreview');
                }

            } catch (error) {
                showResult('singleResult', `❌ 测试失败: ${error.message}`, 'error');
                hideResult('faviconPreview');
            } finally {
                testing = false;
                btn.disabled = false;
                btn.textContent = '测试Favicon获取';
            }
        }

        // 查看候选URLs
        async function testFaviconUrls() {
            const url = document.getElementById('testUrl').value.trim();
            if (!url) {
                showResult('singleResult', '请输入要测试的URL', 'error');
                return;
            }

            try {
                if (!window.Utils || !window.Utils.Url) {
                    throw new Error('工具类未正确加载');
                }

                const faviconUrls = window.Utils.Url.getFaviconUrls(url);
                
                let result = `📋 候选Favicon URLs (${faviconUrls.length}个):\n\n`;
                faviconUrls.forEach((faviconUrl, index) => {
                    result += `${index + 1}. ${faviconUrl}\n`;
                });

                showResult('singleResult', result, 'info');
                hideResult('faviconPreview');

            } catch (error) {
                showResult('singleResult', `❌ 获取候选URLs失败: ${error.message}`, 'error');
            }
        }

        // 批量测试
        async function runBatchTest() {
            if (testing) return;

            testing = true;
            const btn = document.getElementById('batchTestBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';

            const testUrls = [
                'https://www.google.com',
                'https://github.com',
                'https://www.baidu.com',
                'https://stackoverflow.com',
                'https://www.bilibili.com',
                'https://www.zhihu.com',
                'https://www.youtube.com',
                'http://*************:8080',
                'http://localhost:3000',
                'http://jenkins.local'
            ];

            const progressDiv = document.getElementById('batchProgress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const resultsDiv = document.getElementById('batchResults');

            progressDiv.style.display = 'block';
            resultsDiv.innerHTML = '';

            try {
                for (let i = 0; i < testUrls.length; i++) {
                    const url = testUrls[i];
                    const progress = ((i + 1) / testUrls.length) * 100;
                    
                    progressBar.style.width = progress + '%';
                    progressText.textContent = `测试中... (${i + 1}/${testUrls.length}) ${url}`;

                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'batch-item';
                    itemDiv.innerHTML = `<h5>🔍 ${url}</h5><p>测试中...</p>`;
                    resultsDiv.appendChild(itemDiv);

                    try {
                        const startTime = Date.now();
                        const faviconUrl = await window.Utils.Url.getBestFavicon(url);
                        const endTime = Date.now();
                        const duration = endTime - startTime;

                        if (faviconUrl) {
                            itemDiv.className = 'batch-item success';
                            itemDiv.innerHTML = `
                                <h5>✅ ${url}</h5>
                                <p><img src="${faviconUrl}" class="favicon" onerror="this.style.display='none'">${faviconUrl}</p>
                                <p>耗时: ${duration}ms</p>
                            `;
                        } else {
                            itemDiv.className = 'batch-item error';
                            itemDiv.innerHTML = `
                                <h5>❌ ${url}</h5>
                                <p>未能获取到有效的favicon</p>
                                <p>耗时: ${duration}ms</p>
                            `;
                        }
                    } catch (error) {
                        itemDiv.className = 'batch-item error';
                        itemDiv.innerHTML = `
                            <h5>❌ ${url}</h5>
                            <p>错误: ${error.message}</p>
                        `;
                    }

                    // 添加小延迟，避免过快的请求
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                progressText.textContent = '✅ 批量测试完成！';

            } catch (error) {
                progressText.textContent = `❌ 批量测试失败: ${error.message}`;
            } finally {
                testing = false;
                btn.disabled = false;
                btn.textContent = '运行批量测试';
                setTimeout(() => {
                    progressDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 清空批量测试结果
        function clearBatchResults() {
            document.getElementById('batchResults').innerHTML = '';
            document.getElementById('batchProgress').style.display = 'none';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Favicon测试页面已加载');
            
            // 检查工具类是否正确加载
            if (window.Utils && window.Utils.Url) {
                console.log('✅ 工具类加载成功');
                showResult('singleResult', '✅ 工具类加载成功，可以开始测试', 'success');
            } else {
                console.error('❌ 工具类加载失败');
                showResult('singleResult', '❌ 工具类加载失败，请检查 assets/js/utils/helpers.js 文件', 'error');
            }
        });
    </script>
</body>
</html>
