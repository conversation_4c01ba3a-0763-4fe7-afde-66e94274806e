<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单填充测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .favicon-preview {
            width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }
        .test-urls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-urls h4 {
            margin-top: 0;
            color: #495057;
        }
        .test-urls button {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin: 2px;
            font-size: 12px;
        }
        .test-urls button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 表单填充功能测试</h1>
        <p>此页面模拟主应用的添加网站表单，用于测试内网地址的自动填充功能。</p>

        <div class="test-urls">
            <h4>🔗 快速测试地址：</h4>
            <button onclick="fillUrl('http://*************:8080')">*************:8080</button>
            <button onclick="fillUrl('http://localhost:3000')">localhost:3000</button>
            <button onclick="fillUrl('http://jenkins.local')">jenkins.local</button>
            <button onclick="fillUrl('http://*********:9000')">*********:9000</button>
            <button onclick="fillUrl('http://grafana.corp:3000')">grafana.corp:3000</button>
            <button onclick="fillUrl('http://admin.internal')">admin.internal</button>
            <button onclick="fillUrl('https://www.baidu.com')">baidu.com (外网)</button>
        </div>

        <form id="siteForm">
            <div class="form-group">
                <label for="url">网站地址 *</label>
                <input type="url" id="url" name="url" placeholder="输入网站地址，如：http://*************:8080" required>
            </div>

            <div class="form-group">
                <label for="name">网站名称 *</label>
                <input type="text" id="name" name="name" placeholder="网站名称（自动获取）" required>
            </div>

            <div class="form-group">
                <label for="description">网站描述</label>
                <textarea id="description" name="description" placeholder="网站描述（自动获取）"></textarea>
            </div>

            <div class="form-group">
                <label for="category">分类</label>
                <select id="category" name="category">
                    <option value="other">其他</option>
                    <option value="work">工作</option>
                    <option value="development">开发</option>
                    <option value="entertainment">娱乐</option>
                    <option value="social">社交</option>
                    <option value="news">新闻</option>
                    <option value="education">教育</option>
                    <option value="shopping">购物</option>
                    <option value="tools">工具</option>
                    <option value="reference">参考</option>
                </select>
            </div>

            <div class="form-group">
                <label for="favicon">网站图标 (Favicon)</label>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <input type="url" id="favicon" name="favicon" placeholder="图标地址（自动获取）" readonly style="flex: 1;">
                    <img id="faviconPreview" class="favicon-preview" style="display: none;" alt="图标预览">
                </div>
            </div>

            <div class="progress" id="progress" style="display: none;">
                <div class="progress-bar" id="progressBar"></div>
            </div>

            <button type="button" class="btn" onclick="fetchWebsiteInfo()" id="fetchBtn">
                🔍 自动获取网站信息
            </button>
            <button type="button" class="btn btn-secondary" onclick="clearForm()">
                🗑️ 清空表单
            </button>
            <button type="submit" class="btn" style="background: #28a745;">
                💾 保存网站
            </button>
        </form>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <!-- 引入工具类 -->
    <script src="assets/js/utils/helpers.js"></script>

    <script>
        let fetchingInfo = false;

        // 快速填充URL
        function fillUrl(url) {
            document.getElementById('url').value = url;
            showResult(`已填充URL: ${url}`, 'info');
        }

        // 清空表单
        function clearForm() {
            document.getElementById('siteForm').reset();
            document.getElementById('faviconPreview').style.display = 'none';
            hideProgress();
            hideResult();
            showResult('表单已清空', 'info');
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // 隐藏结果
        function hideResult() {
            document.getElementById('result').style.display = 'none';
        }

        // 显示进度条
        function showProgress(percent = 0) {
            const progressDiv = document.getElementById('progress');
            const progressBar = document.getElementById('progressBar');
            progressDiv.style.display = 'block';
            progressBar.style.width = percent + '%';
        }

        // 隐藏进度条
        function hideProgress() {
            document.getElementById('progress').style.display = 'none';
        }

        // 更新favicon预览
        function updateFaviconPreview(faviconUrl) {
            const preview = document.getElementById('faviconPreview');
            if (faviconUrl) {
                preview.src = faviconUrl;
                preview.style.display = 'inline-block';
            } else {
                preview.style.display = 'none';
            }
        }

        // 主要的网站信息抓取函数
        async function fetchWebsiteInfo() {
            if (fetchingInfo) {
                showResult('正在抓取中，请稍候...', 'info');
                return;
            }

            const url = document.getElementById('url').value.trim();
            if (!url) {
                showResult('请先输入网站地址', 'error');
                return;
            }

            // 验证URL格式
            let targetUrl;
            try {
                targetUrl = new URL(url);
            } catch (error) {
                showResult('请输入有效的网站地址（如：https://example.com）', 'error');
                return;
            }

            fetchingInfo = true;
            const fetchBtn = document.getElementById('fetchBtn');
            fetchBtn.disabled = true;
            fetchBtn.textContent = '🔍 抓取中...';

            try {
                console.log('🔍 开始自动抓取网站信息');
                console.log('目标URL:', url);

                // 检查工具类是否加载
                if (!window.Utils || !window.Utils.WebsiteMeta) {
                    throw new Error('工具类未正确加载');
                }

                showProgress(20);
                showResult('正在抓取网站信息...', 'info');

                // 调用网站信息抓取
                const metadata = await window.Utils.WebsiteMeta.fetchWebsiteMetadata(url);
                
                showProgress(60);
                console.log('📝 获取到的metadata:', metadata);

                if (metadata) {
                    showProgress(80);
                    
                    let updates = [];
                    let hasUpdates = false;

                    // 更新网站名称
                    if (metadata.title && metadata.title.trim()) {
                        const cleanTitle = metadata.title.trim();
                        const isInternalNetwork = window.Utils.WebsiteMeta.isInternalNetwork(url);
                        
                        console.log('🏷️ 标题更新检查:', {
                            cleanTitle,
                            hostname: targetUrl.hostname,
                            isInternalNetwork,
                            shouldUpdate: cleanTitle !== targetUrl.hostname || isInternalNetwork
                        });
                        
                        if (cleanTitle !== targetUrl.hostname || isInternalNetwork) {
                            document.getElementById('name').value = cleanTitle;
                            updates.push(`标题: ${cleanTitle}`);
                            hasUpdates = true;
                            console.log('✅ 标题已更新:', cleanTitle);
                        }
                    }

                    // 更新网站描述
                    if (metadata.description && metadata.description.trim()) {
                        const cleanDescription = metadata.description.trim();
                        console.log('📝 描述更新:', cleanDescription);
                        document.getElementById('description').value = cleanDescription;
                        updates.push(`描述: ${cleanDescription.substring(0, 50)}...`);
                        hasUpdates = true;
                        console.log('✅ 描述已更新');
                    }

                    // 更新分类
                    if (metadata.category && metadata.category !== 'other') {
                        const isInternalNetwork = window.Utils.WebsiteMeta.isInternalNetwork(url);
                        const currentCategory = document.getElementById('category').value;
                        
                        console.log('🏷️ 分类更新检查:', {
                            category: metadata.category,
                            isInternalNetwork,
                            currentCategory,
                            shouldUpdate: isInternalNetwork || currentCategory === 'other' || !currentCategory
                        });
                        
                        if (isInternalNetwork || currentCategory === 'other' || !currentCategory) {
                            document.getElementById('category').value = metadata.category;
                            const categoryName = window.Utils.WebsiteMeta.getCategoryName(metadata.category);
                            updates.push(`分类: ${categoryName}`);
                            hasUpdates = true;
                            console.log('✅ 分类已更新:', metadata.category);
                        }
                    }

                    // 更新favicon
                    if (metadata.favicon) {
                        console.log('🎨 Favicon更新:', metadata.favicon);
                        document.getElementById('favicon').value = metadata.favicon;
                        updateFaviconPreview(metadata.favicon);
                        updates.push('图标: 已获取');
                        hasUpdates = true;
                        console.log('✅ Favicon已更新');
                    }

                    showProgress(100);

                    // 显示结果
                    if (hasUpdates) {
                        const updateSummary = updates.length > 3
                            ? `${updates.slice(0, 2).join(', ')} 等${updates.length}项信息`
                            : updates.join(', ');

                        showResult(`✅ 网站信息获取成功！已更新: ${updateSummary}`, 'success');
                        console.log('✅ 表单更新完成');
                    } else {
                        // 即使没有获取到完整信息，也尝试设置基本信息
                        if (!document.getElementById('name').value.trim()) {
                            document.getElementById('name').value = targetUrl.hostname;
                        }

                        showResult(`⚠️ 网站信息获取部分成功，已设置基本信息。${metadata.error ? `错误: ${metadata.error}` : ''}`, 'info');
                    }
                } else {
                    showResult('❌ 未能获取到网站信息', 'error');
                }

            } catch (error) {
                console.error('❌ 网站信息抓取失败:', error);
                showResult(`❌ 获取失败: ${error.message}`, 'error');
            } finally {
                fetchingInfo = false;
                fetchBtn.disabled = false;
                fetchBtn.textContent = '🔍 自动获取网站信息';
                setTimeout(hideProgress, 1000);
            }
        }

        // 表单提交处理
        document.getElementById('siteForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const siteData = Object.fromEntries(formData);
            
            console.log('💾 保存网站数据:', siteData);
            showResult(`✅ 网站信息已保存！\n${JSON.stringify(siteData, null, 2)}`, 'success');
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 表单填充测试页面已加载');
            
            // 检查工具类是否正确加载
            if (window.Utils && window.Utils.WebsiteMeta) {
                console.log('✅ 工具类加载成功');
                showResult('✅ 工具类加载成功，可以开始测试', 'success');
            } else {
                console.error('❌ 工具类加载失败');
                showResult('❌ 工具类加载失败，请检查 assets/js/utils/helpers.js 文件', 'error');
            }
        });
    </script>
</body>
</html>
