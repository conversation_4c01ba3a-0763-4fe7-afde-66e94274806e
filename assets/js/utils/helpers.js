// 辅助函数工具库

// 时间格式化工具
const TimeUtils = {
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
        if (!date) return '';

        const d = new Date(date);
        if (isNaN(d.getTime())) return '';

        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    // 相对时间
    timeAgo(date) {
        if (!date) return '';

        const now = new Date();
        const past = new Date(date);
        const diff = now - past;

        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        const months = Math.floor(days / 30);
        const years = Math.floor(months / 12);

        if (years > 0) return `${years}年前`;
        if (months > 0) return `${months}个月前`;
        if (days > 0) return `${days}天前`;
        if (hours > 0) return `${hours}小时前`;
        if (minutes > 0) return `${minutes}分钟前`;
        return '刚刚';
    },

    // 格式化时长
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
        }
        return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
    }
};

// 字符串工具
const StringUtils = {
    // 截断文本
    truncate(text, length = 100, suffix = '...') {
        if (!text || text.length <= length) return text;
        return text.substring(0, length) + suffix;
    },

    // 首字母大写
    capitalize(text) {
        if (!text) return '';
        return text.charAt(0).toUpperCase() + text.slice(1);
    },

    // 驼峰转换
    camelCase(text) {
        return text.replace(/[-_\s]+(.)?/g, (_, c) => c ? c.toUpperCase() : '');
    },

    // 短横线命名
    kebabCase(text) {
        return text.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
    },

    // 生成随机字符串
    randomString(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    // 生成UUID
    uuid() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },

    // 高亮搜索关键词
    highlight(text, keyword) {
        if (!keyword) return text;
        const regex = new RegExp(`(${keyword})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
};

// 数组工具
const ArrayUtils = {
    // 数组去重
    unique(array, key = null) {
        if (key) {
            const seen = new Set();
            return array.filter(item => {
                const value = item[key];
                if (seen.has(value)) {
                    return false;
                }
                seen.add(value);
                return true;
            });
        }
        return [...new Set(array)];
    },

    // 数组分组
    groupBy(array, key) {
        return array.reduce((groups, item) => {
            const group = item[key];
            groups[group] = groups[group] || [];
            groups[group].push(item);
            return groups;
        }, {});
    },

    // 数组排序
    sortBy(array, key, order = 'asc') {
        return [...array].sort((a, b) => {
            const aVal = a[key];
            const bVal = b[key];

            if (aVal < bVal) return order === 'asc' ? -1 : 1;
            if (aVal > bVal) return order === 'asc' ? 1 : -1;
            return 0;
        });
    },

    // 数组分页
    paginate(array, page = 1, size = 10) {
        const start = (page - 1) * size;
        const end = start + size;
        return {
            data: array.slice(start, end),
            total: array.length,
            page: page,
            size: size,
            totalPages: Math.ceil(array.length / size)
        };
    },

    // 数组搜索
    search(array, keyword, fields = []) {
        if (!keyword) return array;

        const searchTerm = keyword.toLowerCase();
        return array.filter(item => {
            if (fields.length === 0) {
                // 搜索所有字符串字段
                return Object.values(item).some(value =>
                    typeof value === 'string' &&
                    value.toLowerCase().includes(searchTerm)
                );
            } else {
                // 搜索指定字段
                return fields.some(field => {
                    const value = item[field];
                    return typeof value === 'string' &&
                           value.toLowerCase().includes(searchTerm);
                });
            }
        });
    }
};

// URL工具
const UrlUtils = {
    // 验证URL
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    // 获取域名
    getDomain(url) {
        try {
            return new URL(url).hostname;
        } catch {
            return '';
        }
    },

    // 获取favicon URL
    getFaviconUrl(url) {
        try {
            const domain = this.getDomain(url);
            return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
        } catch {
            return '';
        }
    },

    // 构建查询字符串
    buildQuery(params) {
        return Object.keys(params)
            .filter(key => params[key] !== null && params[key] !== undefined)
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
            .join('&');
    },

    // 获取多种favicon URL
    getFaviconUrls(url) {
        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname;
            const protocol = urlObj.protocol;

            return [
                `${protocol}//${domain}/favicon.ico`,
                `${protocol}//${domain}/apple-touch-icon.png`,
                `${protocol}//${domain}/apple-touch-icon-precomposed.png`,
                `https://www.google.com/s2/favicons?domain=${domain}&sz=32`,
                `https://icons.duckduckgo.com/ip3/${domain}.ico`,
                `https://www.google.com/s2/favicons?domain=${domain}&sz=64`
            ];
        } catch {
            return [];
        }
    },

    // 测试favicon URL是否有效
    async testFaviconUrl(url) {
        try {
            await fetch(url, {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache',
                signal: AbortSignal.timeout(3000)
            });
            return true;
        } catch {
            return false;
        }
    },

    // 获取最佳favicon - 改进版本
    async getBestFavicon(siteUrl) {
        console.log('🎯 开始获取最佳favicon:', siteUrl);

        const faviconUrls = this.getFaviconUrls(siteUrl);
        console.log('📋 候选favicon URLs:', faviconUrls);

        // 并发测试多个favicon URL，提高速度
        const testPromises = faviconUrls.map(async (faviconUrl, index) => {
            try {
                const isValid = await new Promise((resolve) => {
                    const img = new Image();

                    // 设置跨域属性
                    img.crossOrigin = 'anonymous';

                    const timeout = setTimeout(() => {
                        resolve(false);
                    }, 2000); // 减少超时时间到2秒

                    img.onload = () => {
                        clearTimeout(timeout);
                        // 检查图片尺寸，确保不是1x1的占位图
                        if (img.width > 1 && img.height > 1) {
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    };

                    img.onerror = () => {
                        clearTimeout(timeout);
                        resolve(false);
                    };

                    img.src = faviconUrl;
                });

                if (isValid) {
                    console.log(`✅ 找到有效favicon (${index + 1}/${faviconUrls.length}):`, faviconUrl);
                    return { url: faviconUrl, index, valid: true };
                }
            } catch (error) {
                console.warn(`❌ 测试favicon失败 (${index + 1}/${faviconUrls.length}):`, faviconUrl, error);
            }
            return { url: faviconUrl, index, valid: false };
        });

        try {
            // 等待所有测试完成，但使用Promise.allSettled避免单个失败影响整体
            const results = await Promise.allSettled(testPromises);

            // 找到第一个有效的favicon
            for (const result of results) {
                if (result.status === 'fulfilled' && result.value.valid) {
                    return result.value.url;
                }
            }
        } catch (error) {
            console.warn('并发测试favicon时出错:', error);
        }

        // 如果都失败了，返回Google服务的默认图标
        const fallbackUrl = this.getFaviconUrl(siteUrl);
        console.log('🔄 使用回退favicon:', fallbackUrl);
        return fallbackUrl;
    },

    // 解析查询字符串
    parseQuery(queryString) {
        const params = {};
        const pairs = queryString.replace(/^\?/, '').split('&');

        pairs.forEach(pair => {
            const [key, value] = pair.split('=');
            if (key) {
                params[decodeURIComponent(key)] = decodeURIComponent(value || '');
            }
        });

        return params;
    }
};

// 文件工具
const FileUtils = {
    // 格式化文件大小
    formatSize(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 获取文件扩展名
    getExtension(filename) {
        return filename.split('.').pop().toLowerCase();
    },

    // 读取文件为文本
    readAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(e);
            reader.readAsText(file);
        });
    },

    // 读取文件为DataURL
    readAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(e);
            reader.readAsDataURL(file);
        });
    },

    // 下载文件
    download(content, filename, type = 'text/plain') {
        const blob = new Blob([content], { type });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
};

// 颜色工具
const ColorUtils = {
    // 生成随机颜色
    random() {
        return '#' + Math.floor(Math.random() * 16777215).toString(16);
    },

    // 十六进制转RGB
    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    },

    // RGB转十六进制
    rgbToHex(r, g, b) {
        return '#' + [r, g, b].map(x => {
            const hex = x.toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        }).join('');
    },

    // 获取对比色
    getContrast(hex) {
        const rgb = this.hexToRgb(hex);
        if (!rgb) return '#000000';

        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        return brightness > 128 ? '#000000' : '#ffffff';
    }
};

// 防抖和节流
const ThrottleUtils = {
    // 防抖
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 网站元数据抓取工具
const WebsiteMetaUtils = {
    // 智能分类映射
    categoryKeywords: {
        'search': ['搜索', 'search', 'google', 'baidu', 'bing', 'yahoo', 'duckduckgo'],
        'social': ['社交', 'social', 'facebook', 'twitter', 'instagram', 'linkedin', 'weibo', 'qq', 'wechat', 'tiktok'],
        'news': ['新闻', 'news', '资讯', 'media', '日报', '时报', '新华', 'cnn', 'bbc'],
        'development': ['开发', 'dev', 'github', 'gitlab', 'stackoverflow', 'coding', 'programming', 'code', 'api', 'docs'],
        'entertainment': ['娱乐', 'entertainment', 'video', 'music', 'game', 'youtube', 'netflix', 'spotify', '游戏', '音乐', '视频'],
        'education': ['教育', 'education', 'learn', 'course', 'university', 'school', '学习', '课程', '大学'],
        'shopping': ['购物', 'shop', 'buy', 'store', 'amazon', 'taobao', 'jd', 'tmall', '商城', '电商'],
        'work': ['工作', 'work', 'office', 'business', 'enterprise', 'corp', '企业', '公司', '办公'],
        'finance': ['金融', 'finance', 'bank', 'money', 'payment', 'invest', '银行', '支付', '投资'],
        'technology': ['科技', 'tech', 'technology', 'ai', 'blockchain', 'cloud', '人工智能', '区块链']
    },

    // 简化的网站信息获取（基于域名和常见模式）
    async getBasicWebsiteInfo(url) {
        try {
            const domain = UrlUtils.getDomain(url);

            // 基于域名的智能推测
            let title = domain;
            let description = '';
            let category = 'other';

            // 常见网站的预设信息
            const knownSites = {
                'jd.com': { title: '京东', description: '京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！', category: 'shopping' },
                'taobao.com': { title: '淘宝网', description: '淘宝网 - 亚洲较大的网上交易平台', category: 'shopping' },
                'tmall.com': { title: '天猫', description: '天猫，品质购物之选', category: 'shopping' },
                'baidu.com': { title: '百度', description: '百度一下，你就知道', category: 'search' },
                'google.com': { title: 'Google', description: 'Search the world\'s information', category: 'search' },
                'github.com': { title: 'GitHub', description: 'Where the world builds software', category: 'development' },
                'stackoverflow.com': { title: 'Stack Overflow', description: 'Where Developers Learn, Share, & Build Careers', category: 'development' },
                'zhihu.com': { title: '知乎', description: '有问题，就会有答案', category: 'social' },
                'weibo.com': { title: '微博', description: '随时随地发现新鲜事', category: 'social' },
                'bilibili.com': { title: '哔哩哔哩', description: '国内知名的视频弹幕网站', category: 'entertainment' },
                'youtube.com': { title: 'YouTube', description: 'Enjoy the videos and music you love', category: 'entertainment' }
            };

            // 检查是否是已知网站
            for (const [knownDomain, info] of Object.entries(knownSites)) {
                if (domain.includes(knownDomain)) {
                    title = info.title;
                    description = info.description;
                    category = info.category;
                    break;
                }
            }

            // 如果不是已知网站，尝试智能分类
            if (category === 'other') {
                category = this.categorizeWebsite(title, description, [], url);
            }

            return {
                title,
                description,
                category,
                keywords: [],
                success: true
            };
        } catch (error) {
            console.warn('获取基本网站信息失败:', error);
            return {
                title: UrlUtils.getDomain(url),
                description: '',
                category: 'other',
                keywords: [],
                success: false,
                error: error.message
            };
        }
    },

    // 检测是否为内网地址
    isInternalNetwork(url) {
        try {
            const urlObj = new URL(url);
            const hostname = urlObj.hostname;

            // 检查是否为IP地址
            const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
            if (ipRegex.test(hostname)) {
                const parts = hostname.split('.').map(Number);

                // 检查私有IP地址范围
                // 10.0.0.0/8 (10.0.0.0 - **************)
                if (parts[0] === 10) return true;

                // **********/12 (********** - **************)
                if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return true;

                // ***********/16 (*********** - ***************)
                if (parts[0] === 192 && parts[1] === 168) return true;

                // *********/8 (localhost)
                if (parts[0] === 127) return true;

                // ***********/16 (link-local)
                if (parts[0] === 169 && parts[1] === 254) return true;
            }

            // 检查常见的内网域名
            const internalDomains = [
                'localhost',
                '.local',
                '.internal',
                '.intranet',
                '.corp',
                '.lan'
            ];

            return internalDomains.some(domain =>
                hostname === domain.replace('.', '') ||
                hostname.endsWith(domain)
            );

        } catch (error) {
            return false;
        }
    },

    // 处理内网地址的特殊逻辑
    async handleInternalNetwork(url) {
        console.log('🏠 开始处理内网地址:', url);

        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname;

            // 尝试直接访问获取基本信息（可能会因为CORS失败，但我们可以尝试）
            let metadata = {
                title: '',
                description: '',
                keywords: [],
                category: 'other',
                favicon: '',
                success: false,
                source: 'internal'
            };

            // 尝试直接fetch（可能因CORS失败，但值得一试）
            try {
                console.log('🌐 尝试直接访问内网地址...');
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors', // 尝试CORS
                    credentials: 'omit',
                    signal: AbortSignal.timeout(5000)
                });

                if (response.ok) {
                    const htmlContent = await response.text();
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(htmlContent, 'text/html');
                    metadata = this.extractMetadata(doc, url);
                    metadata.success = true;
                    metadata.source = 'direct';
                    console.log('✅ 直接访问内网地址成功');
                }
            } catch (directError) {
                console.log('❌ 直接访问失败（预期行为）:', directError.message);
                // 这是预期的，因为大多数内网地址会有CORS限制
            }

            // 如果直接访问失败，使用智能推测
            if (!metadata.success) {
                console.log('🧠 使用智能推测生成内网地址信息...');
                metadata = this.generateInternalNetworkInfo(url);
            }

            // 尝试获取favicon（即使网页内容获取失败，favicon可能可以获取）
            try {
                console.log('🎨 尝试获取内网地址favicon...');
                metadata.favicon = await this.getInternalNetworkFavicon(url);
            } catch (faviconError) {
                console.warn('内网favicon获取失败:', faviconError.message);
                metadata.favicon = this.generateDefaultFavicon(url);
            }

            console.log('🏠 内网地址处理完成:', metadata);
            return metadata;

        } catch (error) {
            console.error('❌ 内网地址处理失败:', error);

            // 返回基本的回退信息
            return {
                title: UrlUtils.getDomain(url) || '内网站点',
                description: '内网地址，无法自动获取详细信息',
                keywords: ['内网', '局域网'],
                category: 'work',
                favicon: this.generateDefaultFavicon(url),
                success: false,
                error: error.message,
                source: 'fallback-internal'
            };
        }
    },

    // 智能推测内网地址信息
    generateInternalNetworkInfo(url) {
        const urlObj = new URL(url);
        const hostname = urlObj.hostname;
        const pathname = urlObj.pathname;

        let title = hostname;
        let description = '内网站点';
        let category = 'work';

        // 基于域名和路径的智能推测
        const patterns = {
            // 常见的内网服务
            'jenkins': { title: 'Jenkins CI/CD', description: 'Jenkins持续集成服务', category: 'development' },
            'gitlab': { title: 'GitLab', description: 'GitLab代码仓库', category: 'development' },
            'jira': { title: 'Jira', description: 'Jira项目管理', category: 'work' },
            'confluence': { title: 'Confluence', description: 'Confluence知识库', category: 'work' },
            'nexus': { title: 'Nexus Repository', description: 'Nexus制品仓库', category: 'development' },
            'sonar': { title: 'SonarQube', description: '代码质量检测', category: 'development' },
            'grafana': { title: 'Grafana', description: 'Grafana监控面板', category: 'work' },
            'kibana': { title: 'Kibana', description: 'Kibana日志分析', category: 'work' },
            'prometheus': { title: 'Prometheus', description: 'Prometheus监控', category: 'work' },
            'harbor': { title: 'Harbor', description: 'Harbor镜像仓库', category: 'development' },
            'rancher': { title: 'Rancher', description: 'Rancher容器管理', category: 'development' },
            'portainer': { title: 'Portainer', description: 'Portainer容器管理', category: 'development' },
            'nacos': { title: 'Nacos', description: 'Nacos配置中心', category: 'development' },
            'apollo': { title: 'Apollo', description: 'Apollo配置中心', category: 'development' },
            'admin': { title: '管理后台', description: '系统管理后台', category: 'work' },
            'dashboard': { title: '仪表板', description: '系统仪表板', category: 'work' },
            'monitor': { title: '监控系统', description: '系统监控平台', category: 'work' },
            'wiki': { title: 'Wiki', description: '内部Wiki文档', category: 'work' },
            'docs': { title: '文档系统', description: '内部文档系统', category: 'work' },
            'api': { title: 'API服务', description: 'API接口服务', category: 'development' },
            'test': { title: '测试环境', description: '测试环境系统', category: 'development' },
            'dev': { title: '开发环境', description: '开发环境系统', category: 'development' },
            'staging': { title: '预发布环境', description: '预发布环境系统', category: 'development' },
            'prod': { title: '生产环境', description: '生产环境系统', category: 'work' }
        };

        // 检查域名和路径中的关键词
        const fullUrl = (hostname + pathname).toLowerCase();
        for (const [keyword, info] of Object.entries(patterns)) {
            if (fullUrl.includes(keyword)) {
                title = info.title;
                description = info.description;
                category = info.category;
                break;
            }
        }

        // 如果是IP地址，尝试根据端口推测
        const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
        if (ipRegex.test(hostname)) {
            const port = urlObj.port;
            const portPatterns = {
                '8080': { title: 'Web应用服务', description: '基于8080端口的Web应用', category: 'development' },
                '8081': { title: 'Web应用服务', description: '基于8081端口的Web应用', category: 'development' },
                '9000': { title: 'SonarQube', description: 'SonarQube代码质量检测', category: 'development' },
                '3000': { title: 'Grafana', description: 'Grafana监控面板', category: 'work' },
                '5601': { title: 'Kibana', description: 'Kibana日志分析', category: 'work' },
                '9090': { title: 'Prometheus', description: 'Prometheus监控', category: 'work' },
                '8848': { title: 'Nacos', description: 'Nacos配置中心', category: 'development' },
                '8070': { title: 'Apollo', description: 'Apollo配置中心', category: 'development' }
            };

            if (port && portPatterns[port]) {
                title = portPatterns[port].title;
                description = portPatterns[port].description;
                category = portPatterns[port].category;
            } else if (title === hostname) {
                title = `内网服务 (${hostname})`;
                description = `运行在 ${hostname} 的内网服务`;
            }
        }

        return {
            title,
            description,
            keywords: ['内网', '局域网', category === 'development' ? '开发' : '工作'],
            category,
            favicon: '',
            success: true,
            source: 'intelligent-guess'
        };
    },

    // 获取内网地址的favicon
    async getInternalNetworkFavicon(url) {
        const urlObj = new URL(url);
        const baseUrl = `${urlObj.protocol}//${urlObj.host}`;

        // 常见的favicon路径
        const faviconPaths = [
            '/favicon.ico',
            '/favicon.png',
            '/apple-touch-icon.png',
            '/apple-touch-icon-precomposed.png',
            '/assets/favicon.ico',
            '/static/favicon.ico',
            '/public/favicon.ico',
            '/img/favicon.ico',
            '/images/favicon.ico'
        ];

        // 尝试直接访问favicon（可能成功，因为favicon通常没有CORS限制）
        for (const path of faviconPaths) {
            try {
                const faviconUrl = baseUrl + path;

                // 使用Image对象测试favicon是否存在
                const isValid = await new Promise((resolve) => {
                    const img = new Image();
                    img.crossOrigin = 'anonymous';

                    const timeout = setTimeout(() => resolve(false), 2000);

                    img.onload = () => {
                        clearTimeout(timeout);
                        resolve(img.width > 1 && img.height > 1);
                    };

                    img.onerror = () => {
                        clearTimeout(timeout);
                        resolve(false);
                    };

                    img.src = faviconUrl;
                });

                if (isValid) {
                    console.log('✅ 找到内网favicon:', faviconUrl);
                    return faviconUrl;
                }
            } catch (error) {
                console.debug('内网favicon测试失败:', path, error.message);
            }
        }

        // 如果都失败了，返回默认图标
        return this.generateDefaultFavicon(url);
    },

    // 生成默认favicon
    generateDefaultFavicon(url) {
        const urlObj = new URL(url);
        const hostname = urlObj.hostname;

        // 如果是IP地址，使用服务器图标
        const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
        if (ipRegex.test(hostname)) {
            return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">🖥️</text></svg>';
        }

        // 根据域名类型选择图标
        if (hostname.includes('dev') || hostname.includes('test')) {
            return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">🔧</text></svg>';
        } else if (hostname.includes('admin') || hostname.includes('manage')) {
            return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">⚙️</text></svg>';
        } else if (hostname.includes('monitor') || hostname.includes('grafana')) {
            return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">📊</text></svg>';
        } else {
            return 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y=".9em" font-size="90">🏠</text></svg>';
        }
    },

    // 抓取网站元数据 - 重构版本
    async fetchWebsiteMetadata(url) {
        console.log('🔍 开始抓取网站元数据:', url);

        // 验证URL
        if (!UrlUtils.isValidUrl(url)) {
            throw new Error('无效的URL');
        }

        // 检查是否为内网地址
        const isInternal = this.isInternalNetwork(url);
        console.log('🏠 内网地址检测:', { url, isInternal });

        if (isInternal) {
            console.log('🏠 检测到内网地址，使用内网处理策略');
            return await this.handleInternalNetwork(url);
        }

        // 多种代理服务列表，提高成功率
        const proxyServices = [
            {
                name: 'AllOrigins',
                url: (targetUrl) => `https://api.allorigins.win/get?url=${encodeURIComponent(targetUrl)}`,
                timeout: 8000,
                parseResponse: (data) => data.contents
            },
            {
                name: 'CORS-Anywhere-Alternative',
                url: (targetUrl) => `https://corsproxy.io/?${encodeURIComponent(targetUrl)}`,
                timeout: 8000,
                parseResponse: (data) => data
            },
            {
                name: 'ThingProxy',
                url: (targetUrl) => `https://thingproxy.freeboard.io/fetch/${targetUrl}`,
                timeout: 8000,
                parseResponse: (data) => data
            }
        ];

        // 尝试使用代理服务获取网页内容
        for (const proxy of proxyServices) {
            try {
                console.log(`🌐 尝试使用 ${proxy.name} 代理服务`);

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), proxy.timeout);

                const response = await fetch(proxy.url(url), {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json, text/html, */*',
                        'User-Agent': 'Mozilla/5.0 (compatible; WebsiteMetaBot/1.0)'
                    },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    const contentType = response.headers.get('content-type') || '';
                    let htmlContent = '';

                    if (contentType.includes('application/json')) {
                        const data = await response.json();
                        htmlContent = proxy.parseResponse(data);
                    } else {
                        htmlContent = await response.text();
                    }

                    if (htmlContent && htmlContent.trim()) {
                        console.log(`✅ ${proxy.name} 代理服务成功获取内容`);

                        // 解析HTML内容
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(htmlContent, 'text/html');

                        // 提取元数据
                        const metadata = this.extractMetadata(doc, url);

                        // 获取favicon
                        if (!metadata.favicon) {
                            try {
                                metadata.favicon = await UrlUtils.getBestFavicon(url);
                            } catch (faviconError) {
                                console.warn('获取favicon失败:', faviconError);
                                metadata.favicon = UrlUtils.getFaviconUrl(url);
                            }
                        }

                        console.log('🎉 网站元数据抓取成功:', metadata);
                        return {
                            ...metadata,
                            success: true,
                            source: proxy.name
                        };
                    }
                }
            } catch (proxyError) {
                console.warn(`❌ ${proxy.name} 代理服务失败:`, proxyError.message);
                continue; // 尝试下一个代理服务
            }
        }

        // 所有代理服务都失败，使用基本信息获取
        console.log('🔄 所有代理服务失败，使用基本信息获取方法');
        try {
            const basicInfo = await this.getBasicWebsiteInfo(url);

            // 尝试获取favicon
            let favicon = '';
            try {
                favicon = await UrlUtils.getBestFavicon(url);
            } catch (faviconError) {
                console.warn('获取favicon失败:', faviconError);
                favicon = UrlUtils.getFaviconUrl(url);
            }

            return {
                ...basicInfo,
                favicon: favicon,
                success: true,
                source: 'basic'
            };
        } catch (error) {
            console.error('❌ 基本信息获取也失败:', error);

            // 最后的回退方案
            return {
                title: UrlUtils.getDomain(url) || '未知网站',
                description: '无法获取网站描述',
                keywords: [],
                category: 'other',
                favicon: UrlUtils.getFaviconUrl(url),
                success: false,
                error: error.message,
                source: 'fallback'
            };
        }
    },

    // 从HTML文档中提取元数据
    extractMetadata(doc, url) {
        const metadata = {
            title: '',
            description: '',
            keywords: [],
            category: 'other',
            favicon: '',
            success: true
        };

        try {
            // 提取标题
            const titleElement = doc.querySelector('title');
            if (titleElement && titleElement.textContent) {
                metadata.title = titleElement.textContent.trim();
            }

            // 如果没有标题，尝试其他方式
            if (!metadata.title) {
                const h1Element = doc.querySelector('h1');
                if (h1Element && h1Element.textContent) {
                    metadata.title = h1Element.textContent.trim();
                } else {
                    metadata.title = UrlUtils.getDomain(url);
                }
            }

            // 提取描述 - 多种方式
            const descriptionSelectors = [
                'meta[name="description"]',
                'meta[property="og:description"]',
                'meta[name="twitter:description"]',
                'meta[property="description"]',
                'meta[name="Description"]'
            ];

            for (const selector of descriptionSelectors) {
                const element = doc.querySelector(selector);
                if (element && element.getAttribute('content')) {
                    metadata.description = element.getAttribute('content').trim();
                    break;
                }
            }

            // 如果还没有描述，尝试从页面内容提取
            if (!metadata.description) {
                const pElements = doc.querySelectorAll('p');
                for (const p of pElements) {
                    if (p.textContent && p.textContent.trim().length > 20) {
                        metadata.description = p.textContent.trim().substring(0, 200);
                        break;
                    }
                }
            }

            // 提取关键词
            const keywordsMeta = doc.querySelector('meta[name="keywords"]') ||
                               doc.querySelector('meta[name="Keywords"]');
            if (keywordsMeta && keywordsMeta.getAttribute('content')) {
                const keywordsContent = keywordsMeta.getAttribute('content');
                metadata.keywords = keywordsContent.split(',')
                    .map(k => k.trim())
                    .filter(k => k && k.length > 0);
            }

            // 提取favicon - 多种方式
            const faviconSelectors = [
                'link[rel="icon"]',
                'link[rel="shortcut icon"]',
                'link[rel="apple-touch-icon"]',
                'link[rel="apple-touch-icon-precomposed"]',
                'link[rel="mask-icon"]'
            ];

            for (const selector of faviconSelectors) {
                const element = doc.querySelector(selector);
                if (element && element.getAttribute('href')) {
                    let faviconUrl = element.getAttribute('href');

                    // 处理相对路径
                    if (faviconUrl.startsWith('//')) {
                        faviconUrl = new URL(url).protocol + faviconUrl;
                    } else if (faviconUrl.startsWith('/')) {
                        faviconUrl = new URL(url).origin + faviconUrl;
                    } else if (!faviconUrl.startsWith('http')) {
                        faviconUrl = new URL(faviconUrl, url).href;
                    }

                    metadata.favicon = faviconUrl;
                    break;
                }
            }

            // 智能分类
            metadata.category = this.categorizeWebsite(metadata.title, metadata.description, metadata.keywords, url);

        } catch (error) {
            console.warn('提取元数据时出错:', error);
            // 确保至少有基本信息
            if (!metadata.title) {
                metadata.title = UrlUtils.getDomain(url);
            }
        }

        return metadata;
    },

    // 智能分类网站
    categorizeWebsite(title, description, keywords, url) {
        const text = `${title} ${description} ${keywords.join(' ')} ${url}`.toLowerCase();

        // 计算每个分类的匹配分数
        const scores = {};

        for (const [category, categoryKeywords] of Object.entries(this.categoryKeywords)) {
            scores[category] = 0;

            for (const keyword of categoryKeywords) {
                if (text.includes(keyword.toLowerCase())) {
                    scores[category] += 1;
                }
            }
        }

        // 找到得分最高的分类
        let bestCategory = 'other';
        let bestScore = 0;

        for (const [category, score] of Object.entries(scores)) {
            if (score > bestScore) {
                bestScore = score;
                bestCategory = category;
            }
        }

        return bestScore > 0 ? bestCategory : 'other';
    },

    // 获取分类的中文名称
    getCategoryName(category) {
        const categoryNames = {
            'search': '搜索引擎',
            'social': '社交媒体',
            'news': '新闻资讯',
            'development': '开发工具',
            'entertainment': '娱乐',
            'education': '教育学习',
            'shopping': '购物',
            'work': '工作',
            'finance': '金融',
            'technology': '科技',
            'other': '其他',
            'default': '默认'
        };

        return categoryNames[category] || '其他';
    }
};

// 导出所有工具
window.Utils = {
    Time: TimeUtils,
    String: StringUtils,
    Array: ArrayUtils,
    Url: UrlUtils,
    File: FileUtils,
    Color: ColorUtils,
    Throttle: ThrottleUtils,
    WebsiteMeta: WebsiteMetaUtils
};

// 兼容模块化导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.Utils;
}
